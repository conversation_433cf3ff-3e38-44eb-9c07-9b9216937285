#!/usr/bin/env python3
"""
Recoater API Client
==================

This module provides a simple Python client for controlling the recoater system
via its REST API. It's based on the existing patterns found in the recoater's
own codebase to ensure maximum compatibility.

For beginners: This is like a "remote control" for your recoater system.

Usage Example:
    from recoater_client import RecoaterClient
    
    # Create connection to recoater
    client = RecoaterClient()
    
    # Check if recoater is ready
    status = client.get_state()
    print(f"Recoater is: {status['state']}")
    
    # Move X-axis to position 100mm
    client.move_x_axis(position=100, speed=30)
"""

import requests
import json
import time
from config import API_BASE_URL, API_TIMEOUT, DEBUG_MODE, LOG_API_CALLS

class RecoaterClient:
    """
    Simple client for controlling the recoater system via REST API.
    
    This class provides easy-to-use methods for all recoater operations,
    using the same patterns as the existing system for maximum compatibility.
    """
    
    def __init__(self, base_url=None, timeout=None):
        """
        Initialize the recoater client.
        
        Args:
            base_url: API base URL (uses config.py default if None)
            timeout: Request timeout in seconds (uses config.py default if None)
        """
        self.base_url = base_url or API_BASE_URL
        self.timeout = timeout or API_TIMEOUT
        
        if DEBUG_MODE:
            print(f"RecoaterClient initialized with URL: {self.base_url}")
    
    def _make_request(self, method, endpoint, **kwargs):
        """
        Make an HTTP request to the recoater API.
        
        This is a helper method that handles all the common request logic,
        including URL construction, error handling, and logging.
        
        Args:
            method: HTTP method ('GET', 'POST', 'PUT', 'DELETE')
            endpoint: API endpoint (e.g., '/state', '/x/motion')
            **kwargs: Additional arguments for requests (json, data, headers, etc.)
        
        Returns:
            requests.Response object
        """
        url = f"{self.base_url}{endpoint}"
        
        # Set default timeout if not provided
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        
        if LOG_API_CALLS:
            print(f"API Call: {method} {url}")
            if 'json' in kwargs:
                print(f"  JSON: {kwargs['json']}")
        
        try:
            response = requests.request(method, url, **kwargs)
            
            if LOG_API_CALLS:
                print(f"  Response: {response.status_code}")
            
            return response
            
        except requests.exceptions.RequestException as e:
            if DEBUG_MODE:
                print(f"Request failed: {e}")
            raise
    
    # ==========================================================================
    # SYSTEM STATUS AND MONITORING
    # ==========================================================================
    
    def get_state(self):
        """
        Get the current system state.
        
        Returns:
            dict: System state information
            Example: {"state": "ready"}
        """
        response = self._make_request('GET', '/state')
        response.raise_for_status()
        return response.json()
    
    def is_ready(self):
        """
        Check if the recoater is ready for operations.
        
        Returns:
            bool: True if ready, False otherwise
        """
        try:
            state = self.get_state()
            return state.get('state') == 'ready'
        except:
            return False
    
    def wait_until_ready(self, max_wait=30):
        """
        Wait until the recoater is ready.
        
        Args:
            max_wait: Maximum time to wait in seconds
            
        Returns:
            bool: True if became ready, False if timeout
        """
        start_time = time.time()
        while time.time() - start_time < max_wait:
            if self.is_ready():
                return True
            time.sleep(0.5)
        return False
    
    # ==========================================================================
    # X-AXIS CONTROL (based on existing patterns)
    # ==========================================================================
    
    def get_x_info(self):
        """
        Get X-axis information (position, status, etc.).
        
        Returns:
            dict: X-axis information
        """
        response = self._make_request('GET', '/x')
        response.raise_for_status()
        return response.json()
    
    def move_x_axis(self, position, speed, mode='absolute'):
        """
        Move X-axis to a specific position.
        
        Args:
            position: Target position in mm
            speed: Movement speed in mm/s
            mode: Movement mode ('absolute', 'relative', or 'homing')
            
        Returns:
            bool: True if command was accepted
        """
        payload = {
            "mode": mode,
            "speed": speed,
            "distance": position
        }
        
        response = self._make_request('POST', '/x/motion', json=payload)
        return response.status_code == 201
    
    def home_x_axis(self, speed=15):
        """
        Home the X-axis (move to reference position).
        
        Args:
            speed: Homing speed in mm/s
            
        Returns:
            bool: True if homing started successfully
        """
        payload = {
            "mode": "homing",
            "speed": speed
        }
        
        response = self._make_request('POST', '/x/motion', json=payload)
        return response.status_code == 201
    
    def stop_x_axis(self):
        """
        Stop X-axis movement.
        
        Returns:
            bool: True if stop command was accepted
        """
        response = self._make_request('DELETE', '/x/motion')
        return response.status_code == 204
    
    # ==========================================================================
    # Z-AXIS CONTROL (based on existing patterns)
    # ==========================================================================
    
    def get_z_info(self):
        """
        Get Z-axis information (position, status, etc.).
        
        Returns:
            dict: Z-axis information
        """
        response = self._make_request('GET', '/z')
        response.raise_for_status()
        return response.json()
    
    def move_z_axis(self, position, speed, mode='absolute'):
        """
        Move Z-axis to a specific position.
        
        Args:
            position: Target position in mm
            speed: Movement speed in mm/s
            mode: Movement mode ('absolute', 'relative', or 'homing')
            
        Returns:
            bool: True if command was accepted
        """
        payload = {
            "mode": mode,
            "speed": speed,
            "distance": position
        }
        
        response = self._make_request('POST', '/z/motion', json=payload)
        return response.status_code == 201
    
    def home_z_axis(self, speed=5):
        """
        Home the Z-axis (move to reference position).
        
        Args:
            speed: Homing speed in mm/s
            
        Returns:
            bool: True if homing started successfully
        """
        payload = {
            "mode": "homing",
            "speed": speed
        }
        
        response = self._make_request('POST', '/z/motion', json=payload)
        return response.status_code == 201
    
    def stop_z_axis(self):
        """
        Stop Z-axis movement.
        
        Returns:
            bool: True if stop command was accepted
        """
        response = self._make_request('DELETE', '/z/motion')
        return response.status_code == 204
    
    # ==========================================================================
    # GRIPPER CONTROL
    # ==========================================================================
    
    def get_gripper_state(self):
        """
        Get current gripper state.
        
        Returns:
            dict: Gripper state information
        """
        response = self._make_request('GET', '/z/gripper')
        response.raise_for_status()
        return response.json()
    
    def set_gripper(self, state):
        """
        Set gripper state (open/close).
        
        Args:
            state: True to close gripper, False to open
            
        Returns:
            bool: True if command was accepted
        """
        payload = {"state": state}
        response = self._make_request('PUT', '/z/gripper', json=payload)
        return response.status_code == 204
    
    def open_gripper(self):
        """Open the gripper."""
        return self.set_gripper(False)
    
    def close_gripper(self):
        """Close the gripper."""
        return self.set_gripper(True)

    # ==========================================================================
    # PRINT JOB MANAGEMENT (based on existing patterns)
    # ==========================================================================

    def start_print_job(self):
        """
        Start a print job.

        Returns:
            bool: True if print job started successfully
        """
        response = self._make_request('POST', '/print/job')
        return response.status_code == 202

    def cancel_print_job(self):
        """
        Cancel the current print job.

        Returns:
            bool: True if cancellation was successful
        """
        response = self._make_request('DELETE', '/print/job')
        return response.status_code == 204

    def get_print_info(self):
        """
        Get information about the current print job.

        Returns:
            dict: Print job information
        """
        response = self._make_request('GET', '/print/info')
        response.raise_for_status()
        return response.json()

    # ==========================================================================
    # PRINT PARAMETERS (based on existing patterns)
    # ==========================================================================

    def get_print_parameters(self):
        """
        Get current print parameters.

        Returns:
            dict: Print parameters
        """
        response = self._make_request('GET', '/print/parameters')
        response.raise_for_status()
        return response.json()

    def set_print_parameters(self, **parameters):
        """
        Set print parameters.

        Args:
            **parameters: Print parameters to set

        Example:
            client.set_print_parameters(
                patterning_speed=30,
                travel_speed=100,
                z_speed=5,
                layer_thickness=0.08
            )

        Returns:
            bool: True if parameters were set successfully
        """
        response = self._make_request('PUT', '/print/parameters', json=parameters)
        return response.status_code == 204

    # ==========================================================================
    # DRUM CONTROL (based on existing patterns)
    # ==========================================================================

    def set_drum_geometry(self, drum_id, geometry_data):
        """
        Set geometry for a specific drum.

        Args:
            drum_id: Drum ID (0, 1, or 2 for 3-drum system)
            geometry_data: Binary geometry data (PNG or CLI format)

        Returns:
            bool: True if geometry was set successfully
        """
        response = self._make_request(
            'PUT',
            f'/geometries/{drum_id}',
            data=geometry_data,
            headers={"Content-Type": "application/octet-stream"}
        )
        return response.status_code == 204

    def delete_drum_geometry(self, drum_id):
        """
        Delete geometry for a specific drum.

        Args:
            drum_id: Drum ID (0, 1, or 2 for 3-drum system)

        Returns:
            bool: True if geometry was deleted successfully
        """
        response = self._make_request('DELETE', f'/geometries/{drum_id}')
        return response.status_code == 204

    # ==========================================================================
    # UTILITY METHODS
    # ==========================================================================

    def test_connection(self):
        """
        Test connection to the recoater system.

        Returns:
            bool: True if connection is working
        """
        try:
            self.get_state()
            return True
        except:
            return False

    def emergency_stop(self):
        """
        Emergency stop - stop all movements and cancel print job.

        Returns:
            dict: Results of stop operations
        """
        results = {
            'x_axis_stopped': False,
            'z_axis_stopped': False,
            'print_job_cancelled': False
        }

        try:
            results['x_axis_stopped'] = self.stop_x_axis()
        except:
            pass

        try:
            results['z_axis_stopped'] = self.stop_z_axis()
        except:
            pass

        try:
            results['print_job_cancelled'] = self.cancel_print_job()
        except:
            pass

        return results
