# APIRecoater - Minimal Dependencies for Recoater Control
# 
# This file contains only the essential dependencies needed to communicate
# with the recoater system via the existing REST API

# HTTP Client Library - for making API calls to the recoater
requests>=2.31.0

# Optional: For better JSON handling and pretty printing
# (requests includes basic JSON support, but these provide enhanced features)
# Uncomment if needed:
# json5>=0.9.0
# pprint

# Optional: For configuration file handling
# Uncomment if you want to use YAML config files:
# pyyaml>=6.0

# Optional: For enhanced error handling and logging
# Uncomment if you want better logging:
# colorlog>=6.7.0

# Development dependencies (optional)
# Uncomment for development:
# pytest>=7.0.0
# black>=23.0.0
# flake8>=6.0.0
