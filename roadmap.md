# **Project Plan: APIRecoater Web GUI**

## 1. Project Charter 

### 1.1. Mission
To create a modern, intuitive, and reliable web-based Graphical User Interface (GUI) for the APIRecoater system. This GUI will replace the cumbersome SwaggerUI, providing non-programmers with a clean and efficient way to monitor and control the recoater.

### 1.2. Core Philosophy
- **Simplicity First**: Prioritize ease of use over feature quantity.
- **Reliability is Key**: The system must be stable and provide constant, accurate feedback.
- **Incremental Development**: Implement in well-defined, stable phases.
- **Adherence to Existing API**: All interactions **must** go through `recoater_client.py`.

### 1.3. Key Requirements
- **Intuitive UI**: Clean, uncluttered interface inspired by the Aerosint UI.
- **Constant Monitoring**: Regular polling for real-time status.
- **High Uptime**: Designed for continuous operation.
- **Cross-Browser Compatibility**: Focus on **Firefox**, Chrome, and Edge.
- **Phased Rollout**: Start with an MVP and expand.

## 2. High-Level Architecture

The architecture remains a Python Flask backend serving a dynamic HTML/CSS/JS frontend, communicating with the hardware via the `recoater_client.py` module.

```mermaid
graph TD
    subgraph "User's Computer"
        A["Web Browser<br/>(Firefox, Chrome, Edge)"]
    end
    
    subgraph "Python Backend Server (Your IPC)"
        B["Flask Web Server<br/>(app.py)"]
        C["RecoaterClient<br/>(recoater_client.py)"]
        D["Configuration<br/>(config.py)"]
    end
    
    subgraph "Recoater Hardware"
        E["Recoater REST API<br/>(*************:8080)"]
    end
    
    A -- "HTTP Requests (e.g., /api/status)" --> B
    B -- "Serves HTML/CSS/JS" --> A
    B -- "Python Function Calls" --> C
    C -- "Reads Settings" --> D
    C -- "HTTP API Calls" --> E
    
    classDef browser fill:#e3f2fd
    classDef backend fill:#fff3e0
    classDef hardware fill:#e8f5e8
    
    class A browser
    class B,C,D backend
    class E hardware
```

## 3. Technology Stack

- **Backend**: **Flask**
- **Frontend**: **HTML5, CSS3, Vanilla JavaScript (ES6+)**
- **Recoater Communication**: `recoater_client.py`
- **Dependency Management**: `requirements.txt` (to be updated with `Flask`)

## 4. New Project Structure

A new directory `APIRecoater-GUI` will house the project.

```
APIRecoater-GUI/
├── app.py
├── recoater_client.py
├── config.py
├── requirements.txt
├── templates/
│   └── index.html
└── static/
    ├── css/
    │   └── style.css
    └── js/
        └── main.js
```

## 5. 🗺️ Phased Implementation Plan

### Phase 1: MVP - "Monitor & Home"

**Goal**: A stable, read-only monitoring dashboard with the most basic, safe control actions. This proves the architecture works and provides immediate value. This covers the display portions of **Aerosint UI section 8.3 (Axis Window)**.

#### UI Mockup (Layout)
*(This remains the same as the initial plan)*
```
+--------------------------------------------------------------------------+
| APIRecoater Control Panel                                    [Status: ●] |
+--------------------------------------------------------------------------+
|  +---------------------------+  +--------------------------------------+  |
|  |       System Status       |  |              Controls                |  |
|  |---------------------------|  |--------------------------------------|  |
|  | State: [      ready      ] |  |  +--------------------------------+  |  |
|  | X-Axis Pos: [   150.2 mm  ] |  |  | [       EMERGENCY STOP         ] |  |  |
|  | X-Axis Homed: [    true   ] |  |  +--------------------------------+  |  |
|  | Z-Axis Pos: [    10.5 mm  ] |  |  +---------------+  +---------------+  |  |
|  | Z-Axis Homed: [    false  ] |  |  | Home X-Axis   |  | Home Z-Axis   |  |  |
|  +---------------------------+  |  +---------------+  +---------------+  |  |
|                                 +--------------------------------------+  |
+--------------------------------------------------------------------------+
| Log: Client connected successfully.                                      |
+--------------------------------------------------------------------------+
```
#### Functionality
- **Backend (`app.py`):** Create endpoints for `/`, `/api/status`, `/api/home_x`, `/api/home_z`, `/api/emergency_stop`. The `/api/status` endpoint will be the heart of the monitoring.
- **Frontend (`main.js`):** Implement polling to `/api/status` every 2 seconds, update the UI with the response, and add click handlers for the control buttons.

#### Success Criteria
- [ ] Web page loads and status panel updates automatically with live data.
- [ ] Status indicator is green (connected) or red (disconnected).
- [ ] Homing and Emergency Stop buttons work correctly.
- [ ] The UI is stable and responsive.

---

### Phase 2: Full Manual Control

**Goal**: Empower users with direct, intuitive control over axis movement and the Z-axis gripper, covering all manual actions from **Aerosint UI section 8.3 (Axis Window)**.

#### UI Additions
- A new **Manual Control** panel will be added.
- **X-Axis Controls**: Input for `Position (mm)`, `Speed (mm/s)`, and a "Move X" button.
- **Z-Axis Controls**: Input for `Position (mm)`, `Speed (mm/s)`, a "Move Z" button, and a **"Set Current Z as Zero"** button.
- **Gripper Controls**: "Open Gripper" and "Close Gripper" buttons, with an indicator for the current state (e.g., "Open", "Closed").

#### Functionality
1.  **Backend (`app.py`):**
    - **New Endpoints:**
        - `POST /api/move_x`: Accepts `{"position": float, "speed": float}`. Calls `client.move_x_axis()`.
        - `POST /api/move_z`: Accepts `{"position": float, "speed": float}`. Calls `client.move_z_axis()`.
        - `POST /api/set_z_offset`: Accepts `{"offset": float}`. This will be a *software-level* offset. The backend will store this Z-offset and apply it to all subsequent `move_z` calls. For example, if offset is 10mm and user requests move to 5mm, the backend calls `client.move_z_axis(position=15, ...)`.
        - `POST /api/gripper`: Accepts `{"state": "open" | "close"}`. Calls `client.open_gripper()` or `client.close_gripper()`.
    - **Modify `GET /api/status`** to also include gripper state from `client.get_gripper_state()`.

2.  **Frontend (`main.js`):**
    - Add event listeners for the new buttons.
    - The "Set Current Z as Zero" button will read the current Z position from the status panel and send it to `/api/set_z_offset`.
    - Implement client-side input validation for position and speed.

#### Success Criteria
- [ ] User can command the X and Z axes to move to specific absolute positions.
- [ ] User can set a new virtual Z=0, and subsequent moves are relative to this new zero.
- [ ] User can open and close the gripper.
- [ ] The UI correctly displays the current gripper state.

---

### Phase 3: Complete Print Job Management

**Goal**: Enable users to configure all necessary parameters, upload geometries, and manage the full lifecycle of a print job. This covers **Aerosint UI sections 8.2 (Print Window)** and **8.4 (Configuration Window)**.

#### UI Additions
- A new tab or section for **"Print Setup"**.
- **Build Area Config**: Radio buttons for "Circle" or "Rectangle" and input fields for dimensions (as per section 8.4).
- **Geometry Upload**:
    - A section for each of the 3 drums.
    - Each section will have a file input (`<input type="file">`) and an "Upload" button.
- **Print Parameters Panel**:
    - Inputs for: `Layer Start`, `Layer End`, `Filling Drum ID`, `Patterning Speed`, `Travel Speed`.
    - Checkbox for `Powder Saving`.
- **Print Control Panel**:
    - "Start Print" button.
    - "Cancel Print" button.
- **Print Info Panel** (in the main status area):
    - Display `Current Layer / Total Layers`.

#### Functionality
1.  **Backend (`app.py`):**
    - **New Endpoints:**
        - `POST /api/upload_geometry/<int:drum_id>`: Receives file data and calls `client.set_drum_geometry(drum_id, file_data)`.
        - `POST /api/print/parameters`: Receives a large JSON object with all print parameters and calls `client.set_print_parameters(**params)`. The build area config is for UI/preview purposes and doesn't need a direct API call.
        - `POST /api/print/start`: Calls `client.start_print_job()`.
        - `POST /api/print/cancel`: Calls `client.cancel_print_job()`.
    - **Modify `GET /api/status`** to include print info (`n_layers`, `last_layer`) from `client.get_print_info()`.

2.  **Frontend (`main.js`):**
    - Add logic to handle file selection and upload via `fetch` with a `FormData` body.
    - Consolidate all print parameter inputs into a single object to send to the backend.
    - Implement context-aware UI changes: disable manual controls when a print is active; show print progress.

#### Success Criteria
- [ ] User can upload a geometry file for each drum.
- [ ] User can configure and set all required print parameters.
- [ ] User can start a print job.
- [ ] User can cancel a running print job.
- [ ] The UI displays the current layer progress during a print.

---

### Phase 4: Advanced Calibration & Recoater Tuning

**Goal**: Implement the fine-tuning controls for advanced users and technicians, covering **Aerosint UI section 8.1 (Recoater Window)**.

> **! IMPORTANT PREREQUISITE !**
> The existing `recoater_client.py` and `openapi.yaml` **do not** currently expose endpoints for direct drum/hopper/pressure control. This phase assumes these endpoints will be added to the recoater's REST API. The GUI will be built to call these *theoretical* endpoints, and development can proceed once the endpoint definitions are agreed upon.

#### UI Additions
- A new tab or section for **"Recoater Tuning"**.
- A simple representation of the recoater (e.g., three "Drum" buttons, three "Hopper" buttons). Clicking one will populate the control panel with its specific parameters.
- **When Drum is Selected**:
    - Inputs for `Ejection Pressure`, `Suction Pressure`, `Manual Turns`, `Manual Speed`, and `Theta Offset`.
    - "Rotate Drum" and "Home Drum" buttons.
- **When Hopper is Selected**:
    - Inputs for scraper `Distance (µm)`.
    - Radio buttons for `Actuator (Front, Back, Both)`.
    - "Move Scraper" and "Home Scraper" buttons.

#### Functionality
1.  **Backend (`app.py`): (Theoretical Endpoints)**
    - `POST /api/recoater/drum/<int:drum_id>/config`: Set pressure/offset. Accepts `{"ejection": float, "suction": float, "theta_offset": float}`.
    - `POST /api/recoater/drum/<int:drum_id>/motion`: Start manual rotation. Accepts `{"turns": int, "speed": float}`.
    - `POST /api/recoater/hopper/<int:hopper_id>/move`: Move scraper blade. Accepts `{"distance": int, "actuator": "both"}`.
    - (And corresponding `.../home` endpoints for each component).

2.  **Frontend (`main.js`):**
    - Implement the dynamic control panel that changes based on which component (Drum/Hopper) is selected.
    - Add event handlers to call the new "recoater tuning" endpoints.

#### Success Criteria
- [ ] The backend API specification for advanced control is finalized.
- [ ] User can select a specific drum or hopper to configure.
- [- ] User can set pressures and offsets for each drum.
- [ ] User can manually command drum rotation.
- [ ] User can manually command scraper blade movement.
- [ ] All advanced controls are functional and stable.

## 7. 🚀 Getting Started Guide (for Development)
1.  **Setup Environment**: `python -m venv venv` and activate.
2.  **Install Dependencies**: Update `requirements.txt` with `Flask` and `pip install -r requirements.txt`.
3.  **Configure Network**: Update `RECOATER_API_ADDRESS` in `config.py` to `*************`.
4.  **Run the Application**: `python app.py`.
5.  **Access**: Open Firefox and navigate to `http://<your-ipc-ip-address>:5001`.