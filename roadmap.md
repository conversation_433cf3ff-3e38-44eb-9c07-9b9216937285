# Project Plan: APIRecoater Web GUI

## 1. 🎯 Project Charter

### 1.1. Mission
To create a modern, intuitive, and reliable web-based Graphical User Interface (GUI) for the APIRecoater system. This GUI will replace the cumbersome SwaggerUI, providing non-programmers with a clean and efficient way to monitor and control the recoater.

### 1.2. Core Philosophy
- **Simplicity First**: Prioritize ease of use over feature quantity. Every element must be intuitive.
- **Reliability is Key**: The system must be stable and provide constant, accurate feedback, reflecting the standards of industrial control software.
- **Incremental Development**: Implement the project in well-defined phases, ensuring each phase is a stable, usable product before moving to the next.
- **Adherence to Existing API**: All recoater interactions **must** go through the established `recoater_client.py` module, ensuring we reuse existing, tested logic.

### 1.3. Key Requirements
- **Intuitive UI**: Clean, uncluttered interface inspired by the Aerosint UI.
- **Constant Monitoring**: The UI must regularly poll the recoater for real-time status updates (axis positions, system state).
- **High Uptime**: Designed for continuous operation.
- **Cross-Browser Compatibility**: Must function flawlessly on modern browsers, with a specific focus on **Firefox**.
- **Phased Rollout**: Start with a minimal viable product (MVP) and expand functionality in subsequent phases.

## 2. 🏗️ High-Level Architecture

The GUI will be a web application served by a Python backend. This separates the presentation layer (what the user sees) from the control logic.

```mermaid
graph TD
    subgraph "User's Computer"
        A[Web Browser<br/>(Firefox, Chrome, Edge)]
    end

    subgraph "Python Backend Server (Your IPC)"
        B[Flask Web Server<br/>(app.py)]
        C[RecoaterClient<br/>(recoater_client.py)]
        D[Configuration<br/>(config.py)]
    end

    subgraph "Recoater Hardware"
        E[Recoater REST API<br/>(*************:8080)]
    end

    A -- HTTP Requests (e.g., /api/status) --> B
    B -- Serves HTML/CSS/JS --> A
    B -- Python Function Calls --> C
    C -- Reads Settings --> D
    C -- HTTP API Calls --> E

    classDef browser fill:#e3f2fd
    classDef backend fill:#fff3e0
    classDef hardware fill:#e8f5e8

    class A browser
    class B,C,D backend
    class E hardware
```

**Data Flow for Monitoring:**
1. The user's **Browser** runs JavaScript that polls the **Flask Server** every 1-2 seconds at an endpoint like `/api/status`.
2. The **Flask Server** receives the request and uses the **RecoaterClient** to get the latest state and axis info from the **Recoater Hardware**.
3. The **Flask Server** bundles this information into a single JSON response and sends it back to the **Browser**.
4. The **Browser**'s JavaScript updates the UI with the new data.

**Data Flow for Control:**
1. The user clicks a button (e.g., "Home X-Axis") in the **Browser**.
2. The **Browser**'s JavaScript sends a request (e.g., `POST /api/home_x`) to the **Flask Server**.
3. The **Flask Server** calls the corresponding function in the **RecoaterClient** (e.g., `client.home_x_axis()`).
4. The **RecoaterClient** sends the command to the **Recoater Hardware**.
5. The system returns a success/failure message, which is relayed to the user's browser.

## 3. 🛠️ Technology Stack

- **Backend**: **Flask**. A lightweight and powerful Python web framework. It's simple to set up and ideal for creating the API that our frontend will consume.
- **Frontend**:
    - **HTML5**: For the structure of the web page.
    - **CSS3**: For styling, to create the clean, industrial look.
    - **JavaScript (Vanilla JS - ES6+)**: For all dynamic behavior, including polling and handling user interactions. No complex frameworks like React/Vue in early phases to maintain simplicity.
- **Recoater Communication**: The existing `recoater_client.py` will be used as the sole interface to the hardware.
- **Dependency Management**: `requirements.txt` will be updated to include `Flask`.

## 4. 📁 New Project Structure

A new directory `APIRecoater-GUI` will be created alongside the existing files. The `recoater_client.py` and `config.py` will be copied into it to keep the project self-contained.

```
APIRecoater-GUI/
├── app.py                     # The main Flask backend server
├── recoater_client.py         # Copy of the existing client
├── config.py                  # Copy of the existing config
├── requirements.txt           # Project dependencies (requests, Flask)
├── templates/                 # Folder for HTML files (Flask convention)
│   └── index.html             # The single-page UI for our application
└── static/                    # Folder for CSS and JS files
    ├── css/
    │   └── style.css          # All styling for the GUI
    └── js/
        └── main.js            # All frontend JavaScript logic
```

## 5. 🗺️ Phased Implementation Plan

### Phase 1: The Minimum Viable Product (MVP) - "Monitor & Home"

**Goal**: A stable, read-only monitoring dashboard with basic, safe control actions. This proves the architecture works end-to-end.

#### UI Mockup (Layout)
```
+--------------------------------------------------------------------------+
| APIRecoater Control Panel                                    [Status: ●] |
+--------------------------------------------------------------------------+
|                                                                          |
|  +---------------------------+  +--------------------------------------+  |
|  |       System Status       |  |              Controls                |  |
|  |---------------------------|  |--------------------------------------|  |
|  |                           |  |                                      |  |
|  | State: [      ready      ] |  |  +--------------------------------+  |  |
|  |                           |  |  | [       EMERGENCY STOP         ] |  |  |
|  | X-Axis Pos: [   150.2 mm  ] |  |  +--------------------------------+  |  |
|  | X-Axis Homed: [    true   ] |  |                                      |  |
|  |                           |  |  +---------------+  +---------------+  |  |
|  | Z-Axis Pos: [    10.5 mm  ] |  |  | Home X-Axis   |  | Home Z-Axis   |  |  |
|  | Z-Axis Homed: [    false  ] |  |  +---------------+  +---------------+  |  |
|  |                           |  |                                      |  |
|  +---------------------------+  |                                      |  |
|                                 +--------------------------------------+  |
|                                                                          |
+--------------------------------------------------------------------------+
| Log: Client connected successfully.                                      |
+--------------------------------------------------------------------------+
```

#### Functionality
1.  **Backend (`app.py`):**
    - Create a Flask app instance.
    - Instantiate `RecoaterClient` on startup.
    - **Endpoint `GET /`**: Renders `templates/index.html`.
    - **Endpoint `GET /api/status`**:
        - Calls `client.get_state()`, `client.get_x_info()`, `client.get_z_info()`.
        - Combines the results into a single JSON object.
        - Example response:
          ```json
          {
            "connection": "ok",
            "state": "ready",
            "x": {"position": 150.2, "homed": true},
            "z": {"position": 10.5, "homed": false}
          }
          ```
        - If connection to recoater fails, return `{"connection": "error"}`.
    - **Endpoint `POST /api/home_x`**: Calls `client.home_x_axis()` and returns success/failure.
    - **Endpoint `POST /api/home_z`**: Calls `client.home_z_axis()` and returns success/failure.
    - **Endpoint `POST /api/emergency_stop`**: Calls `client.emergency_stop()` and returns success/failure.

2.  **Frontend (`main.js`):**
    - **Polling**: On page load, start a `setInterval` function to call `fetch('/api/status')` every 2 seconds.
    - **UI Updates**:
        - On receiving new data from `/api/status`, update the text content of the corresponding HTML elements (e.g., `#state-value`, `#x-pos-value`).
        - Change the color of the main status indicator (`#connection-status`) based on the `connection` field (green for "ok", red for "error").
    - **Event Handlers**:
        - Add `click` event listeners to the "Home X-Axis", "Home Z-Axis", and "EMERGENCY STOP" buttons.
        - These listeners will execute a `fetch` with the `POST` method to the respective API endpoints.
        - **UI Feedback**: When a button is clicked, disable it and show a "working..." state. Re-enable it once the `fetch` call completes (either success or failure).

#### Success Criteria for Phase 1
- [ ] The web page loads correctly when `app.py` is run.
- [ ] The status panel updates automatically every 2 seconds with live data from the recoater.
- [ ] The main status indicator is green when connected, red when disconnected.
- [ ] Clicking the "Home X-Axis" and "Home Z-Axis" buttons successfully triggers the homing sequences on the hardware.
- [ ] Clicking the "EMERGENCY STOP" button stops all motion.
- [ ] The UI remains responsive and stable during all operations.

---

### Phase 2: Manual Axis & Gripper Control

**Goal**: Empower users with direct, intuitive control over axis movement and the Z-axis gripper.

#### UI Additions
- **Axis Control Panel**:
    - Input fields for target `Position (mm)` and `Speed (mm/s)` for both X and Z axes.
    - "Move" buttons for each axis.
- **Gripper Control Panel**:
    - "Open Gripper" and "Close Gripper" buttons.
    - A status indicator for the current gripper state.

#### Functionality
1.  **Backend (`app.py`):**
    - Add new endpoints:
        - `POST /api/move_x`: Accepts JSON `{"position": float, "speed": float}`. Calls `client.move_x_axis()`.
        - `POST /api/move_z`: Accepts JSON `{"position": float, "speed": float}`. Calls `client.move_z_axis()`.
        - `POST /api/open_gripper`: Calls `client.open_gripper()`.
        - `POST /api/close_gripper`: Calls `client.close_gripper()`.
    - Modify `GET /api/status` to also include gripper state from `client.get_gripper_state()`.

2.  **Frontend (`main.js`):**
    - Add event listeners for the new "Move" and "Gripper" buttons.
    - Read values from the position/speed input fields to send in the `fetch` request body.
    - Implement input validation (e.g., ensure numbers are entered) before sending requests.
    - Update the gripper status indicator based on the data from `/api/status`.

---

### Phase 3: Basic Print Job Management

**Goal**: Enable users to manage the full lifecycle of a simple print job.

#### UI Additions
- **Print Parameters Panel**:
    - Input fields for key parameters from `config.py` (e.g., `patterning_speed`, `layer_thickness`).
    - A "Set Parameters" button.
- **Print Job Control Panel**:
    - "Start Print Job" button.
    - "Cancel Print Job" button.
- **Print Info Panel**:
    - Display current layer and total layers from `client.get_print_info()`.

#### Functionality
1.  **Backend (`app.py`):**
    - Add new endpoints:
        - `POST /api/print/parameters`: Accepts a JSON object with parameters. Calls `client.set_print_parameters()`.
        - `POST /api/print/start`: Calls `client.start_print_job()`.
        - `POST /api/print/cancel`: Calls `client.cancel_print_job()`.
    - Modify `GET /api/status` to include print info from `client.get_print_info()` when a job is active.

2.  **Frontend (`main.js`):**
    - Add logic to populate, read, and send print parameters.
    - Update the UI to show print progress.
    - Implement context-aware button disabling (e.g., disable "Start Print" if a job is already running; disable axis controls during a print).

---

## 6. ⚙️ Implementation Details & Code Starters

### `app.py` (Flask Backend)
```python
from flask import Flask, render_template, jsonify, request
from recoater_client import RecoaterClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

app = Flask(__name__)
client = RecoaterClient()

@app.route('/')
def index():
    """Serves the main HTML page."""
    return render_template('index.html')

@app.route('/api/status', methods=['GET'])
def get_status():
    """Endpoint for the frontend to poll for status updates."""
    try:
        if not client.test_connection():
             raise ConnectionError("Failed to connect to recoater")

        state = client.get_state()
        x_info = client.get_x_info()
        z_info = client.get_z_info()

        status_data = {
            "connection": "ok",
            "state": state.get('state', 'unknown'),
            "x": {
                "position": x_info.get('position'),
                "homed": x_info.get('homed')
            },
            "z": {
                "position": z_info.get('position'),
                "homed": z_info.get('homed')
            }
        }
        return jsonify(status_data)

    except Exception as e:
        logging.error(f"Error getting status: {e}")
        return jsonify({"connection": "error", "message": str(e)}), 500

# --- Phase 1 Control Endpoints ---

@app.route('/api/emergency_stop', methods=['POST'])
def emergency_stop():
    """Stops all motion."""
    try:
        result = client.emergency_stop()
        return jsonify({"success": True, "details": result})
    except Exception as e:
        logging.error(f"Error during emergency stop: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/home_x', methods=['POST'])
def home_x():
    """Homes the X-axis."""
    try:
        success = client.home_x_axis()
        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error homing X-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500
        
@app.route('/api/home_z', methods=['POST'])
def home_z():
    """Homes the Z-axis."""
    try:
        success = client.home_z_axis()
        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error homing Z-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

if __name__ == '__main__':
    # Use host='0.0.0.0' to make it accessible on the network
    app.run(host='0.0.0.0', port=5001, debug=True)
```

### `templates/index.html` (Basic Structure)
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APIRecoater Control</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <header>
        <h1>APIRecoater Control Panel</h1>
        <div class="status-indicator">
            Connection: <span id="connection-status" class="status-dot-red"></span>
        </div>
    </header>
    <main>
        <div class="panel" id="status-panel">
            <h2>System Status</h2>
            <p>State: <strong id="state-value">N/A</strong></p>
            <p>X-Axis Position: <strong id="x-pos-value">N/A</strong></p>
            <p>X-Axis Homed: <strong id="x-homed-value">N/A</strong></p>
            <p>Z-Axis Position: <strong id="z-pos-value">N/A</strong></p>
            <p>Z-Axis Homed: <strong id="z-homed-value">N/A</strong></p>
        </div>
        <div class="panel" id="control-panel">
            <h2>Controls</h2>
            <button id="btn-estop" class="btn-danger">EMERGENCY STOP</button>
            <div class="control-group">
                <button id="btn-home-x">Home X-Axis</button>
                <button id="btn-home-z">Home Z-Axis</button>
            </div>
        </div>
    </main>
    <footer>
        <div id="log-panel">Log: Waiting for connection...</div>
    </footer>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
```

### `static/js/main.js` (Frontend Logic)
```javascript
document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Elements ---
    const connectionStatus = document.getElementById('connection-status');
    const stateValue = document.getElementById('state-value');
    const xPosValue = document.getElementById('x-pos-value');
    const xHomedValue = document.getElementById('x-homed-value');
    const zPosValue = document.getElementById('z-pos-value');
    const zHomedValue = document.getElementById('z-homed-value');
    const logPanel = document.getElementById('log-panel');

    const btnEstop = document.getElementById('btn-estop');
    const btnHomeX = document.getElementById('btn-home-x');
    const btnHomeZ = document.getElementById('btn-home-z');

    const POLLING_INTERVAL = 2000; // 2 seconds

    // --- API Functions ---
    async function pollStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.connection === 'ok') {
                updateStatusUI(data);
            } else {
                handleConnectionError(data);
            }
        } catch (error) {
            handleConnectionError({ message: 'Frontend fetch failed. Is the server running?' });
        }
    }

    async function sendCommand(endpoint) {
        try {
            const response = await fetch(endpoint, { method: 'POST' });
            const result = await response.json();
            log(`Command ${endpoint}: ${result.success ? 'Success' : 'Failed'}`);
            return result;
        } catch (error) {
            log(`Command ${endpoint} failed: ${error}`, 'error');
            return { success: false };
        }
    }

    // --- UI Update Functions ---
    function updateStatusUI(data) {
        connectionStatus.className = 'status-dot-green';
        stateValue.textContent = data.state;
        xPosValue.textContent = `${data.x.position.toFixed(3)} mm`;
        xHomedValue.textContent = data.x.homed;
        zPosValue.textContent = `${data.z.position.toFixed(3)} mm`;
        zHomedValue.textContent = data.z.homed;
        log('Status updated successfully.');
    }
    
    function handleConnectionError(error) {
        connectionStatus.className = 'status-dot-red';
        stateValue.textContent = 'ERROR';
        xPosValue.textContent = 'N/A';
        zPosValue.textContent = 'N/A';
        log(`Connection Error: ${error.message || 'Unknown error'}`, 'error');
    }

    function log(message, type = 'info') {
        logPanel.textContent = message;
        logPanel.className = type === 'error' ? 'log-error' : 'log-info';
    }

    // --- Event Listeners ---
    btnEstop.addEventListener('click', () => {
        if (confirm('Are you sure you want to perform an EMERGENCY STOP?')) {
            sendCommand('/api/emergency_stop');
        }
    });

    btnHomeX.addEventListener('click', () => sendCommand('/api/home_x'));
    btnHomeZ.addEventListener('click', () => sendCommand('/api/home_z'));

    // --- Initialization ---
    setInterval(pollStatus, POLLING_INTERVAL);
    pollStatus(); // Initial call
});
```

## 7. 🚀 Getting Started Guide (for Development)
1.  **Setup Environment**:
    - Ensure Python 3.7+ is installed.
    - Create a virtual environment: `python -m venv venv`
    - Activate it: `source venv/bin/activate` (Linux/macOS) or `venv\Scripts\activate` (Windows).
2.  **Install Dependencies**:
    - Create `requirements.txt` with `requests` and `Flask`.
    - Run `pip install -r requirements.txt`.
3.  **Configure Network**:
    - Ensure your IPC is on the same network as the recoater.
    - Update `RECOATER_API_ADDRESS` in `config.py` to the recoater's IP (`*************`).
4.  **Run the Application**:
    - Execute `python app.py` in the terminal.
    - Open a Firefox browser on any machine on the same network.
    - Navigate to `http://<your-ipc-ip-address>:5001`.
5.  **Verify**:
    - Check if the GUI loads and the status panel starts updating with live data.
    - Test the control buttons.

---
**Next Steps for the AI agent**: Begin with Phase 1. Create the specified directory structure and files. Implement the code as outlined in the starters above, then proceed to test against the Phase 1 Success Criteria.