# Connection Manual - Step-by-Step Setup Guide

## 🎯 Overview

This manual will guide you through connecting your IPC (computer) directly to the recoater system using a USB3-to-RJ45 adapter, completely bypassing the Controllino PLC.

## 📋 What You'll Need

### Hardware Requirements:
- [ ] Your IPC/computer with USB3 port
- [ ] USB3-to-RJ45 Ethernet adapter
- [ ] Ethernet cable (Cat5e or Cat6)
- [ ] Access to the recoater system

### Software Requirements:
- [ ] Python 3.7 or newer
- [ ] Administrator/root access on your computer
- [ ] Text editor (Notepad++, VS Code, or similar)

---

## 🔧 Step 1: Hardware Setup

### 1.1 Connect the USB3-to-RJ45 Adapter

1. **Plug the USB3-to-RJ45 adapter** into a USB3 port on your computer
2. **Wait for driver installation** (Windows should install drivers automatically)
3. **Verify the adapter is recognized**:
   - Windows: Check Device Manager → Network adapters
   - Look for "USB Ethernet" or similar device

### 1.2 Connect to Recoater System

1. **Locate the recoater's network port** (usually RJ45 Ethernet connector)
2. **Connect ethernet cable** from your USB adapter to the recoater
3. **Power on the recoater system** and wait for it to fully boot

### 1.3 Verify Physical Connection

**Check the LED indicators:**
- **USB adapter**: Should show power/activity lights
- **Recoater network port**: Should show link/activity lights
- **Both ends**: Should have solid or blinking lights indicating connection

---

## 🌐 Step 2: Network Configuration

### 2.1 Find Your New Network Interface

**Windows:**
1. Open **Control Panel** → **Network and Internet** → **Network Connections**
2. Look for a new "Ethernet" connection (usually named "Ethernet 2" or similar)
3. Note the name - you'll need it for configuration

**Alternative method:**
1. Open **Command Prompt** as Administrator
2. Type: `ipconfig /all`
3. Look for the USB Ethernet adapter

### 2.2 Configure Static IP Address

**Why we need this:** The recoater expects to communicate on a specific network range.

**Windows Configuration:**
1. **Right-click** on your new Ethernet connection
2. Select **Properties**
3. Select **Internet Protocol Version 4 (TCP/IPv4)**
4. Click **Properties**
5. Select **Use the following IP address**
6. Enter these settings:
   ```
   IP address: ***********00
   Subnet mask: *************
   Default gateway: (leave blank)
   DNS servers: (leave blank)
   ```
7. Click **OK** to save

### 2.3 Test Basic Network Connectivity

1. **Open Command Prompt**
2. **Test your own interface**: `ping ***********00`
   - Should get replies from your own computer
3. **Test recoater connection**: `ping 127.0.0.1`
   - This tests if the recoater API is responding

---

## 🐍 Step 3: Python Environment Setup

### 3.1 Verify Python Installation

1. **Open Command Prompt**
2. **Check Python version**: `python --version`
   - Should show Python 3.7 or newer
   - If not installed, download from [python.org](https://python.org)

### 3.2 Navigate to Project Directory

```bash
# Change to your project directory
cd C:\path\to\RecoaterSearch\APIRecoater

# Verify you're in the right place
dir
# Should see: config.py, simple_connection_test.py, requirements.txt
```

### 3.3 Install Required Libraries

```bash
# Install the requests library
pip install -r requirements.txt

# Verify installation
python -c "import requests; print('Requests library installed successfully')"
```

---

## 🔍 Step 4: Configuration

### 4.1 Update config.py

1. **Open config.py** in a text editor
2. **Verify these settings**:
   ```python
   RECOATER_API_ADDRESS = "127.0.0.1"  # Localhost
   RECOATER_API_PORT = 8080             # Standard API port
   API_TIMEOUT = 10.0                   # 10 second timeout
   ```
3. **Save the file**

### 4.2 Understanding the Configuration

- **127.0.0.1**: Special "localhost" address meaning "this computer"
- **Port 8080**: The recoater's API server listens on this port
- **Timeout**: How long to wait for responses before giving up

---

## ✅ Step 5: Connection Test

### 5.1 Run the Basic Connection Test

```bash
# Run the simple connection test
python simple_connection_test.py
```

### 5.2 Expected Results

**SUCCESS (what you want to see):**
```
==================================================
RECOATER CONNECTION TEST
==================================================
Trying to connect to: http://127.0.0.1:8080/api/v3

Step 1: Sending request to /state endpoint...
Step 2: Got response with status code: 200
✅ SUCCESS! Connection to recoater established!

Recoater System Information:
------------------------------
System State: ready

Raw API Response:
{
  "state": "ready"
}

==================================================
🎉 CONGRATULATIONS!
Your connection to the recoater is working!
You can now proceed to more advanced programs.
==================================================
```

**If you see this - YOU'RE DONE! 🎉**

---

## 🚨 Troubleshooting

### Problem: "CONNECTION FAILED - Could not connect"

**Possible Causes & Solutions:**

1. **Recoater system not running**
   - Check if recoater is powered on
   - Verify all systems have finished booting
   - Look for status lights indicating "ready"

2. **Network configuration wrong**
   - Double-check IP address settings
   - Try `ping 127.0.0.1` in command prompt
   - Verify USB adapter is working

3. **USB3-to-RJ45 adapter issues**
   - Try a different USB port
   - Check Device Manager for driver issues
   - Test adapter with another device

4. **Firewall blocking connection**
   - Temporarily disable Windows Firewall
   - Add exception for Python or port 8080
   - Check antivirus software

### Problem: "TIMEOUT - System didn't respond"

**Solutions:**
1. **Increase timeout** in config.py: `API_TIMEOUT = 30.0`
2. **Wait longer** - recoater might be busy
3. **Check system load** - recoater might be overloaded

### Problem: "Wrong IP address in config.py"

**Solutions:**
1. **Verify recoater IP**: Check recoater documentation
2. **Try different addresses**:
   - `127.0.0.1` (localhost)
   - `***********` (common router address)
   - Check recoater's display/settings for its IP

### Problem: Python or pip not found

**Solutions:**
1. **Install Python** from [python.org](https://python.org)
2. **Add Python to PATH** during installation
3. **Use full path**: `C:\Python39\python.exe` instead of just `python`

---

## 🔧 Advanced Configuration

### Custom IP Addresses

If your recoater uses a different IP address:

1. **Find the recoater's IP address**:
   - Check recoater's display/menu
   - Look at existing network configuration
   - Ask system administrator

2. **Update config.py**:
   ```python
   RECOATER_API_ADDRESS = "***********"  # Example: use actual IP
   ```

3. **Update your computer's IP** to match the same network:
   ```
   IP address: *************
   Subnet mask: *************
   ```

### Port Configuration

If the API runs on a different port:

1. **Check recoater documentation** for correct port
2. **Update config.py**:
   ```python
   RECOATER_API_PORT = 8081  # Example: use actual port
   ```

---

## ✅ Verification Checklist

Before proceeding to advanced programs, verify:

- [ ] USB3-to-RJ45 adapter is connected and recognized
- [ ] Ethernet cable connects your adapter to recoater
- [ ] Network interface is configured with correct IP
- [ ] Python and requests library are installed
- [ ] config.py has correct IP address and port
- [ ] simple_connection_test.py shows "SUCCESS"
- [ ] You can see recoater status information

**If all items are checked - you're ready for the next phase!** 🚀

---

## 📞 Getting Help

### If you're still having problems:

1. **Check the error message carefully** - it usually tells you what's wrong
2. **Try each step again** - sometimes a small detail was missed
3. **Test one thing at a time** - isolate the problem
4. **Document what you tried** - helps with troubleshooting

### Common Commands for Diagnostics:

```bash
# Test network connectivity
ping 127.0.0.1
ping ***********

# Check network configuration
ipconfig /all

# Test Python installation
python --version
pip --version

# Test requests library
python -c "import requests; print('OK')"
```

Remember: Every expert was once a beginner having these same problems! Take your time and work through each step carefully.
