"""
APIRecoater Configuration
========================

This file contains all the configuration settings for connecting to and controlling
the recoater system. These settings are based on the existing system configuration
found in DOC/ReverseEngineering/config.py.

For beginners: Think of this as the "address book" for your recoater system.
"""

# =============================================================================
# RECOATER API CONNECTION SETTINGS
# =============================================================================

# The IP address and port where the recoater's API server is running
# Default: 127.0.0.1:8080 (localhost) - change this to your recoater's IP
RECOATER_API_ADDRESS = "*************"
RECOATER_API_PORT = 8080

# Complete API base URL - this is what we'll use to talk to the recoater
API_BASE_URL = f"http://{RECOATER_API_ADDRESS}:{RECOATER_API_PORT}/api/v3"

# Timeout settings (in seconds)
API_TIMEOUT = 10.0  # How long to wait for API responses
CONNECTION_TIMEOUT = 5.0  # How long to wait for initial connection

# =============================================================================
# RECOATER HARDWARE SETTINGS (from existing system)
# =============================================================================

# Number of drums in the recoater system
N_DRUMS = 3

# X-Axis movement limits (in millimeters)
X_MIN_POSITION = 0
X_MAX_POSITION = 800
X_LEFT_PRINT_POSITION = 150
X_RIGHT_PRINT_POSITION = 790

# Z-Axis movement limits (in millimeters)  
Z_MIN_POSITION = 0
Z_MAX_POSITION = 120

# Default movement speeds (in mm/s)
DEFAULT_PATTERNING_SPEED = 30
DEFAULT_TRAVEL_SPEED = 100
DEFAULT_Z_SPEED = 5

# Default print parameters
DEFAULT_LAYER_THICKNESS = 0.08  # mm
DEFAULT_Z_OFFSET = 2  # mm
DEFAULT_X_OFFSET = 41.5  # mm

# =============================================================================
# SAFETY SETTINGS
# =============================================================================

# Enable safety checks (recommended: True)
ENABLE_SAFETY_CHECKS = True

# Maximum safe speeds (safety limits)
MAX_SAFE_X_SPEED = 250  # mm/s
MAX_SAFE_Z_SPEED = 10   # mm/s

# =============================================================================
# DEBUGGING AND LOGGING
# =============================================================================

# Enable debug mode for detailed output
DEBUG_MODE = True

# Enable API request/response logging
LOG_API_CALLS = True

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_api_endpoint(endpoint):
    """
    Helper function to build complete API URLs.
    
    Example:
        get_api_endpoint("/state") returns "http://*************:8080/api/v3/state"
    """
    return f"{API_BASE_URL}{endpoint}"

def validate_position(axis, position):
    """
    Validate if a position is within safe limits.
    
    Args:
        axis: "x" or "z"
        position: position in mm
        
    Returns:
        True if position is safe, False otherwise
    """
    if axis.lower() == "x":
        return X_MIN_POSITION <= position <= X_MAX_POSITION
    elif axis.lower() == "z":
        return Z_MIN_POSITION <= position <= Z_MAX_POSITION
    else:
        return False

def validate_speed(axis, speed):
    """
    Validate if a speed is within safe limits.
    
    Args:
        axis: "x" or "z"  
        speed: speed in mm/s
        
    Returns:
        True if speed is safe, False otherwise
    """
    if axis.lower() == "x":
        return 0 < speed <= MAX_SAFE_X_SPEED
    elif axis.lower() == "z":
        return 0 < speed <= MAX_SAFE_Z_SPEED
    else:
        return False
