# Recoater API Client Documentation

## Table of Contents
- [Introduction](#introduction)
- [Installation](#installation)
- [Quick Start](#quick-start)
- [API Reference](#api-reference)
  - [Initialization](#initialization)
  - [System Status](#system-status)
  - [X-Axis Control](#x-axis-control)
  - [Z-Axis Control](#z-axis-control)
  - [Gripper Control](#gripper-control)
  - [Print Job Management](#print-job-management)
  - [Print Parameters](#print-parameters)
  - [Drum Control](#drum-control)
  - [Utility Methods](#utility-methods)

## Introduction

The `RecoaterClient` provides a Python interface to control the recoater system via its REST API. This documentation covers all available methods with examples.

## Installation

1. Ensure Python 3.6+ is installed
2. Install required packages:
   ```bash
   pip install requests
   ```
3. Import the client:
   ```python
   from recoater_client import RecoaterClient
   ```

## Quick Start

```python
from recoater_client import RecoaterClient

# Initialize client
client = RecoaterClient(base_url="http://recoater-ip:5000")

# Check system status
state = client.get_state()
print(f"System state: {state['state']}")

# Move X-axis to position 100mm at 30mm/s
client.move_x_axis(position=100, speed=30)
```

## API Reference

### Initialization

#### `RecoaterClient(base_url=None, timeout=None)`
Initialize a new Recoater client instance.

**Parameters:**
- `base_url` (str, optional): Base URL of the recoater API. Defaults to value from config.py
- `timeout` (int, optional): Request timeout in seconds. Defaults to value from config.py

**Example:**
```python
client = RecoaterClient(base_url="http://*************:5000", timeout=10)
```

### System Status

#### `get_state()`
Get the current system state.

**Returns:**
- `dict`: System state information

**Example:**
```python
state = client.get_state()
print(f"System state: {state['state']}")
```

#### `is_ready()`
Check if the recoater is ready for operations.

**Returns:**
- `bool`: True if ready, False otherwise

**Example:**
```python
if client.is_ready():
    print("System is ready")
```

#### `wait_until_ready(max_wait=30)`
Wait until the recoater is ready.

**Parameters:**
- `max_wait` (int): Maximum time to wait in seconds (default: 30)

**Returns:**
- `bool`: True if ready within timeout, False otherwise

### X-Axis Control

#### `get_x_info()`
Get X-axis information.

**Returns:**
- `dict`: X-axis status and position

#### `move_x_axis(position, speed, mode='absolute')`
Move X-axis to a specific position.

**Parameters:**
- `position` (float): Target position in mm
- `speed` (float): Movement speed in mm/s
- `mode` (str): Movement mode ('absolute', 'relative', or 'homing')

**Returns:**
- `bool`: True if command was accepted

**Example:**
```python
# Move to absolute position 100mm at 30mm/s
client.move_x_axis(position=100, speed=30)
```

#### `home_x_axis(speed=15)`
Home the X-axis.

**Parameters:**
- `speed` (float): Homing speed in mm/s (default: 15)

**Returns:**
- `bool`: True if homing started

#### `stop_x_axis()`
Stop X-axis movement.

**Returns:**
- `bool`: True if stop command was accepted

### Z-Axis Control

#### `get_z_info()`
Get Z-axis information.

**Returns:**
- `dict`: Z-axis status and position

#### `move_z_axis(position, speed, mode='absolute')`
Move Z-axis to a specific position.

**Parameters:**
- `position` (float): Target position in mm
- `speed` (float): Movement speed in mm/s
- `mode` (str): Movement mode ('absolute', 'relative', or 'homing')

**Returns:**
- `bool`: True if command was accepted

#### `home_z_axis(speed=5)`
Home the Z-axis.

**Parameters:**
- `speed` (float): Homing speed in mm/s (default: 5)

**Returns:**
- `bool`: True if homing started

#### `stop_z_axis()`
Stop Z-axis movement.

**Returns:**
- `bool`: True if stop command was accepted

### Gripper Control

#### `get_gripper_state()`
Get current gripper state.

**Returns:**
- `dict`: Gripper state information

#### `set_gripper(state)`
Set gripper state.

**Parameters:**
- `state` (bool): True to close, False to open

**Returns:**
- `bool`: True if command was accepted

#### `open_gripper()`
Open the gripper.

**Returns:**
- `bool`: True if command was accepted

#### `close_gripper()`
Close the gripper.

**Returns:**
- `bool`: True if command was accepted

### Print Job Management

#### `start_print_job()`
Start a print job.

**Returns:**
- `bool`: True if print job started

#### `cancel_print_job()`
Cancel the current print job.

**Returns:**
- `bool`: True if cancellation was successful

#### `get_print_info()`
Get information about the current print job.

**Returns:**
- `dict`: Print job information

### Print Parameters

#### `get_print_parameters()`
Get current print parameters.

**Returns:**
- `dict`: Print parameters

#### `set_print_parameters(**parameters)`
Set print parameters.

**Parameters:**
- `**parameters`: Print parameters to set

**Example:**
```python
client.set_print_parameters(
    patterning_speed=30,
    travel_speed=100,
    z_speed=5,
    layer_thickness=0.08
)
```

### Drum Control

#### `set_drum_geometry(drum_id, geometry_data)`
Set geometry for a specific drum.

**Parameters:**
- `drum_id` (int): Drum ID (0-2 for 3-drum system)
- `geometry_data` (bytes): Binary geometry data

**Returns:**
- `bool`: True if geometry was set

#### `delete_drum_geometry(drum_id)`
Delete geometry for a specific drum.

**Parameters:**
- `drum_id` (int): Drum ID (0-2 for 3-drum system)

**Returns:**
- `bool`: True if geometry was deleted

## Error Handling

All methods that interact with the API may raise the following exceptions:

- `requests.exceptions.RequestException`: For network-related errors
- `requests.exceptions.HTTPError`: For HTTP error responses (4xx, 5xx)
- `ValueError`: For invalid parameters

**Example error handling:**
```python
try:
    client.move_x_axis(position=100, speed=30)
except requests.exceptions.RequestException as e:
    print(f"Network error: {e}")
except requests.exceptions.HTTPError as e:
    print(f"HTTP error: {e.response.status_code}")
```

## Configuration

The client can be configured using the following environment variables or by modifying `config.py`:

- `API_BASE_URL`: Base URL of the recoater API (default: http://localhost:5000)
- `API_TIMEOUT`: Request timeout in seconds (default: 10)
- `DEBUG_MODE`: Enable debug output (default: False)
- `LOG_API_CALLS`: Log all API calls (default: False)
