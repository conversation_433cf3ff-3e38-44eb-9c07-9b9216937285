<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APIRecoater Control</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <header>
        <h1>APIRecoater Control Panel</h1>
        <div class="status-indicator">
            Connection: <span id="connection-status" class="status-dot-red"></span>
        </div>
    </header>
    <main>
        <div class="panel" id="status-panel">
            <h2>System Status</h2>
            <p>State: <strong id="state-value">N/A</strong></p>
            <p>X-Axis Position: <strong id="x-pos-value">N/A</strong></p>
            <p>X-Axis Homed: <strong id="x-homed-value">N/A</strong></p>
            <p>Z-Axis Position: <strong id="z-pos-value">N/A</strong></p>
            <p>Z-Axis Homed: <strong id="z-homed-value">N/A</strong></p>
        </div>
        <div class="panel" id="control-panel">
            <h2>Controls</h2>
            <button id="btn-estop" class="btn-danger">EMERGENCY STOP</button>
            <div class="control-group">
                <button id="btn-home-x">Home X-Axis</button>
                <button id="btn-home-z">Home Z-Axis</button>
            </div>
        </div>
    </main>
    <footer>
        <div id="log-panel">Log: Waiting for connection...</div>
    </footer>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
