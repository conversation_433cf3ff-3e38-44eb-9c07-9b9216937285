# Technology Textbook for Complete Beginners

## 📚 Table of Contents

1. [What is a Network?](#what-is-a-network)
2. [Understanding IP Addresses](#understanding-ip-addresses)
3. [What is HTTP and REST?](#what-is-http-and-rest)
4. [JSON Data Format](#json-data-format)
5. [Python and the requests Library](#python-and-the-requests-library)
6. [How Our System Works](#how-our-system-works)
7. [Common Problems and Solutions](#common-problems-and-solutions)

---

## 🌐 What is a Network?

### Think of it Like Mail 📮

A computer network is like a postal system:
- **Your computer** = Your house
- **Other computers** = Other houses  
- **Network cables** = Roads
- **Messages** = Letters
- **IP addresses** = House addresses

### In Our Project:
```
Your Computer ←→ USB3-to-RJ45 Adapter ←→ Ethernet Cable ←→ Recoater System
    (You)              (Mailbox)           (Road)         (Friend's House)
```

### Key Point:
Just like you need someone's address to send mail, your computer needs the recoater's "address" to send messages.

---

## 🏠 Understanding IP Addresses

### What is an IP Address?

An IP address is like a house address for computers. It tells other computers where to find a specific device on the network.

### Format:
IP addresses look like: `*************`
- Four numbers separated by dots
- Each number is between 0 and 255

### In Our Project:
- **Your computer**: Gets an IP address automatically
- **Recoater system**: Has IP address `127.0.0.1` (special "localhost" address)
- **Port number**: `8080` (like an apartment number)

### Complete Address:
`http://127.0.0.1:8080` means:
- `http://` = "Use web protocol"
- `127.0.0.1` = "Computer address"  
- `:8080` = "Door number 8080"

---

## 🌍 What is HTTP and REST?

### HTTP = HyperText Transfer Protocol

Think of HTTP like a language that computers use to talk to each other over the internet.

### Real-World Analogy: Restaurant Ordering 🍕

**HTTP Request** = You ordering food:
```
"Hello, I'd like to order a large pizza with pepperoni"
```

**HTTP Response** = Restaurant's reply:
```
"Sure! That'll be $15 and ready in 20 minutes"
```

### HTTP Methods (Types of Requests):

| Method | Restaurant Analogy | Computer Meaning |
|--------|-------------------|------------------|
| GET    | "What's on the menu?" | "Give me information" |
| POST   | "I want to order this" | "Create something new" |
| PUT    | "Change my order to this" | "Update something" |
| DELETE | "Cancel my order" | "Remove something" |

### REST = REpresentational State Transfer

REST is just a way of organizing HTTP requests so they're predictable and easy to use.

### REST Example for Our Recoater:
```
GET /state          → "What's the recoater's current status?"
POST /x/motion      → "Move the X-axis to this position"
PUT /z/gripper      → "Set the gripper to open/closed"
DELETE /print/job   → "Cancel the current print job"
```

---

## 📄 JSON Data Format

### What is JSON?

JSON (JavaScript Object Notation) is a way to organize information that both humans and computers can easily read.

### Think of it Like a Form 📋

**Human Form:**
```
Name: John Smith
Age: 25
City: New York
Hobbies: Reading, Swimming
```

**Same Information in JSON:**
```json
{
  "name": "John Smith",
  "age": 25,
  "city": "New York", 
  "hobbies": ["Reading", "Swimming"]
}
```

### JSON Rules:
1. **Curly braces** `{}` = Container for information
2. **Quotes** `""` = Around text
3. **Colons** `:` = Separate names from values
4. **Commas** `,` = Separate different pieces of information
5. **Square brackets** `[]` = Lists of things

### Recoater JSON Examples:

**System Status:**
```json
{
  "state": "ready"
}
```

**Move X-Axis:**
```json
{
  "mode": "absolute",
  "speed": 30,
  "distance": 100
}
```

---

## 🐍 Python and the requests Library

### What is Python?

Python is a programming language that's designed to be easy to read and write. It's like writing instructions in almost-English.

### What is the requests Library?

The `requests` library is a tool that makes it super easy to send HTTP requests (talk to other computers) from Python.

### Basic Pattern:

```python
import requests

# Send a GET request (ask for information)
response = requests.get("http://127.0.0.1:8080/api/v3/state")

# Check if it worked
if response.status_code == 200:
    print("Success!")
    data = response.json()  # Convert JSON to Python
    print(f"Recoater state: {data['state']}")
else:
    print("Something went wrong")
```

### Step-by-Step Breakdown:

1. **Import**: `import requests` = "Load the requests tool"
2. **Send Request**: `requests.get(...)` = "Ask the recoater for its status"
3. **Check Response**: `response.status_code == 200` = "Did it work?"
4. **Get Data**: `response.json()` = "Convert the answer to Python format"
5. **Use Data**: `data['state']` = "Get the 'state' information"

### Common Status Codes:
- **200** = "Success! Everything worked"
- **404** = "Not found (wrong address)"
- **500** = "Server error (recoater has a problem)"
- **Connection Error** = "Can't reach the recoater"

---

## 🔧 How Our System Works

### The Complete Flow:

```mermaid
sequenceDiagram
    participant You as Your Python Program
    participant API as Recoater API
    participant Hardware as Recoater Hardware
    
    You->>API: HTTP GET /state
    API->>Hardware: Check current status
    Hardware->>API: Status: "ready"
    API->>You: JSON: {"state": "ready"}
    
    You->>API: HTTP POST /x/motion
    API->>Hardware: Move X-axis to position
    Hardware->>API: Movement started
    API->>You: HTTP 201: Success
```

### In Simple Terms:

1. **Your program** sends a message (HTTP request) to the recoater
2. **Recoater's API** receives the message and understands what you want
3. **API** tells the hardware (motors, drums) what to do
4. **Hardware** does the action and reports back
5. **API** sends you a response saying if it worked or not

### Example Conversation:

**You**: "Hey recoater, what's your current status?"
**Recoater**: "I'm ready and waiting for commands"

**You**: "Please move the X-axis to position 100mm at 30mm/s speed"
**Recoater**: "OK, starting movement now"

**You**: "Are you done moving yet?"
**Recoater**: "Yes, I'm now at position 100mm"

---

## 🚨 Common Problems and Solutions

### Problem 1: "Connection refused" or "Cannot connect"

**What it means**: Your computer can't reach the recoater
**Possible causes**:
- Recoater system is turned off
- Wrong IP address in config.py
- USB3-to-RJ45 adapter not working
- Ethernet cable not connected

**Solutions**:
1. Check all physical connections
2. Verify recoater system is running
3. Test with `ping 127.0.0.1` in command prompt
4. Check config.py settings

### Problem 2: "Timeout" errors

**What it means**: The recoater is taking too long to respond
**Possible causes**:
- Recoater is busy with another operation
- Network is slow
- Recoater system is overloaded

**Solutions**:
1. Wait and try again
2. Increase timeout in config.py
3. Check if recoater is busy with other tasks

### Problem 3: "404 Not Found" errors

**What it means**: The API endpoint doesn't exist
**Possible causes**:
- Wrong URL in your code
- API server not running properly
- Typo in endpoint name

**Solutions**:
1. Double-check the URL
2. Compare with working examples
3. Verify API server is running

### Problem 4: "JSON decode error"

**What it means**: The response isn't valid JSON
**Possible causes**:
- Server returned an error page instead of JSON
- Network corruption
- Server malfunction

**Solutions**:
1. Print the raw response: `print(response.text)`
2. Check the status code first
3. Add error handling for non-JSON responses

---

## 🎓 Next Steps

Now that you understand the basics:

1. **Practice**: Try running `simple_connection_test.py`
2. **Experiment**: Modify the code to print different information
3. **Learn**: Read the code comments carefully
4. **Build**: Start with simple programs and gradually add features

Remember: Every expert was once a beginner! Take your time and don't be afraid to experiment.
