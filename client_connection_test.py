#!/usr/bin/env python3
"""
Professional Recoater Connection Test
====================================

This is a professional implementation of the connection test using the
RecoaterClient class. It demonstrates proper error handling, logging, and
use of the client's built-in methods.

Key improvements over simple_connection_test.py:
1. Uses the RecoaterClient abstraction layer
2. Better error handling and reporting
3. Cleaner code organization
4. More informative output
5. Follows Python best practices
"""

import sys
import logging
from recoater_client import RecoaterClient
from config import API_BASE_URL, API_TIMEOUT

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_connection():
    """
    Test connection to the recoater system using RecoaterClient.
    
    Returns:
        bool: True if connection was successful, False otherwise
    """
    logger.info("=" * 50)
    logger.info("PROFESSIONAL RECOATER CONNECTION TEST")
    logger.info("=" * 50)
    logger.info(f"Attempting to connect to: {API_BASE_URL}")
    
    try:
        # Initialize the client
        logger.info("Initializing RecoaterClient...")
        client = RecoaterClient(base_url=API_BASE_URL, timeout=API_TIMEOUT)
        
        # Test the connection using the client's built-in method
        logger.info("Testing connection using RecoaterClient...")
        if client.test_connection():
            # Get detailed system state
            logger.info("Connection successful! Retrieving system state...")
            state = client.get_state()
            
            logger.info("\nRECOATER SYSTEM INFORMATION:")
            logger.info("-" * 50)
            logger.info(f"System State: {state.get('state', 'Unknown')}")
            
            # Get additional system information if available
            try:
                system_info = client.get_system_info()
                logger.info(f"Firmware Version: {system_info.get('version', 'N/A')}")
                logger.info(f"Uptime: {system_info.get('uptime', 'N/A')} seconds")
            except Exception as e:
                logger.warning(f"Could not retrieve additional system info: {e}")
            
            logger.info("\nConnection test completed successfully!")
            return True
        else:
            logger.error("Connection test failed: Could not establish connection")
            return False
            
    except Exception as e:
        logger.error(f"Connection test failed with error: {str(e)}", exc_info=True)
        return False

def main():
    """Main function to run the connection test."""
    success = test_connection()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
