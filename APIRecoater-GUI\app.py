from flask import Flask, render_template, jsonify, request
from recoater_client import RecoaterClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

app = Flask(__name__)
client = RecoaterClient()

@app.route('/')
def index():
    """Serves the main HTML page."""
    return render_template('index.html')

@app.route('/api/status', methods=['GET'])
def get_status():
    """Endpoint for the frontend to poll for status updates."""
    try:
        if not client.test_connection():
             raise ConnectionError("Failed to connect to recoater")

        state = client.get_state()
        x_info = client.get_x_info()
        z_info = client.get_z_info()

        status_data = {
            "connection": "ok",
            "state": state.get('state', 'unknown'),
            "x": {
                "position": x_info.get('position'),
                "homed": x_info.get('homed')
            },
            "z": {
                "position": z_info.get('position'),
                "homed": z_info.get('homed')
            }
        }
        return jsonify(status_data)

    except Exception as e:
        logging.error(f"Error getting status: {e}")
        return jsonify({"connection": "error", "message": str(e)}), 500

# --- Phase 1 Control Endpoints ---

@app.route('/api/emergency_stop', methods=['POST'])
def emergency_stop():
    """Stops all motion."""
    try:
        result = client.emergency_stop()
        return jsonify({"success": True, "details": result})
    except Exception as e:
        logging.error(f"Error during emergency stop: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/home_x', methods=['POST'])
def home_x():
    """Homes the X-axis."""
    try:
        success = client.home_x_axis()
        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error homing X-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500
        
@app.route('/api/home_z', methods=['POST'])
def home_z():
    """Homes the Z-axis."""
    try:
        success = client.home_z_axis()
        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error homing Z-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

if __name__ == '__main__':
    # Use host='0.0.0.0' to make it accessible on the network
    app.run(host='0.0.0.0', port=5001, debug=True)
