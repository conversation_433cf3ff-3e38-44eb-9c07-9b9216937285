#!/usr/bin/env python3
"""
Basic Recoater Control Example
=============================

This program demonstrates basic control of the recoater system using the
RecoaterClient class. It shows how to:

1. Connect to the recoater
2. Check system status
3. Control X and Z axes
4. Control the gripper
5. Handle errors safely

For beginners: This is like a "getting started" tutorial for controlling
your recoater system.

Prerequisites:
1. Run simple_connection_test.py first to verify connection
2. Make sure recoater system is powered on and ready
3. Ensure network connection is properly configured

How to use:
1. Read through the code to understand what it does
2. Run: python basic_control_example.py
3. Follow the prompts and observe the recoater's movements
"""

import time
from recoater_client import RecoaterClient
from config import DEBUG_MODE

def print_separator(title=""):
    """Print a nice separator line with optional title."""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def wait_for_user(message="Press Enter to continue..."):
    """Wait for user input before proceeding."""
    input(f"\n{message}")

def safe_operation(operation_name, operation_func):
    """
    Safely execute an operation with error handling.
    
    Args:
        operation_name: Name of the operation for logging
        operation_func: Function to execute
        
    Returns:
        bool: True if successful, False if failed
    """
    try:
        print(f"Executing: {operation_name}")
        result = operation_func()
        if result:
            print(f"✅ {operation_name} - SUCCESS")
        else:
            print(f"❌ {operation_name} - FAILED (returned False)")
        return result
    except Exception as e:
        print(f"❌ {operation_name} - ERROR: {e}")
        return False

def main():
    """Main demonstration program."""
    print_separator("RECOATER BASIC CONTROL DEMONSTRATION")
    print("This program will demonstrate basic recoater control operations.")
    print("Make sure your recoater is powered on and ready before proceeding.")
    
    wait_for_user("Press Enter when ready to start...")
    
    # ==========================================================================
    # STEP 1: CONNECT TO RECOATER
    # ==========================================================================
    print_separator("STEP 1: CONNECTING TO RECOATER")
    
    try:
        client = RecoaterClient()
        print("RecoaterClient created successfully")
    except Exception as e:
        print(f"❌ Failed to create RecoaterClient: {e}")
        return
    
    # Test connection
    if not safe_operation("Connection Test", client.test_connection):
        print("Cannot proceed without a working connection.")
        print("Please check your setup and run simple_connection_test.py first.")
        return
    
    # ==========================================================================
    # STEP 2: CHECK SYSTEM STATUS
    # ==========================================================================
    print_separator("STEP 2: CHECKING SYSTEM STATUS")
    
    try:
        state = client.get_state()
        print(f"System State: {state}")
        
        if client.is_ready():
            print("✅ Recoater is READY for operations")
        else:
            print("⚠️  Recoater is NOT ready")
            print("Current state:", state.get('state', 'unknown'))
            
            wait_for_user("Press Enter to continue anyway, or Ctrl+C to exit...")
            
    except Exception as e:
        print(f"❌ Failed to get system status: {e}")
        return
    
    # ==========================================================================
    # STEP 3: X-AXIS DEMONSTRATIONS
    # ==========================================================================
    print_separator("STEP 3: X-AXIS CONTROL")
    
    wait_for_user("About to demonstrate X-axis control. Press Enter to continue...")
    
    # Get current X-axis info
    try:
        x_info = client.get_x_info()
        print(f"Current X-axis info: {x_info}")
    except Exception as e:
        print(f"⚠️  Could not get X-axis info: {e}")
    
    # Home X-axis
    print("\n1. Homing X-axis...")
    if safe_operation("X-axis Homing", lambda: client.home_x_axis(speed=15)):
        print("Waiting for homing to complete...")
        time.sleep(3)  # Give it time to start moving
        
        # Wait for movement to finish (simple approach)
        print("Homing in progress... (waiting 10 seconds)")
        time.sleep(10)
    
    # Move to specific position
    print("\n2. Moving X-axis to position 100mm...")
    if safe_operation("X-axis Move to 100mm", 
                     lambda: client.move_x_axis(position=100, speed=30)):
        print("Movement started. Waiting for completion...")
        time.sleep(5)
    
    # Move to another position
    print("\n3. Moving X-axis to position 200mm...")
    if safe_operation("X-axis Move to 200mm", 
                     lambda: client.move_x_axis(position=200, speed=30)):
        print("Movement started. Waiting for completion...")
        time.sleep(5)
    
    # ==========================================================================
    # STEP 4: Z-AXIS DEMONSTRATIONS
    # ==========================================================================
    print_separator("STEP 4: Z-AXIS CONTROL")
    
    wait_for_user("About to demonstrate Z-axis control. Press Enter to continue...")
    
    # Get current Z-axis info
    try:
        z_info = client.get_z_info()
        print(f"Current Z-axis info: {z_info}")
    except Exception as e:
        print(f"⚠️  Could not get Z-axis info: {e}")
    
    # Home Z-axis
    print("\n1. Homing Z-axis...")
    if safe_operation("Z-axis Homing", lambda: client.home_z_axis(speed=5)):
        print("Waiting for homing to complete...")
        time.sleep(3)
        print("Homing in progress... (waiting 10 seconds)")
        time.sleep(10)
    
    # Move Z-axis up
    print("\n2. Moving Z-axis up to 10mm...")
    if safe_operation("Z-axis Move to 10mm", 
                     lambda: client.move_z_axis(position=10, speed=5)):
        print("Movement started. Waiting for completion...")
        time.sleep(3)
    
    # Move Z-axis down
    print("\n3. Moving Z-axis down to 5mm...")
    if safe_operation("Z-axis Move to 5mm", 
                     lambda: client.move_z_axis(position=5, speed=5)):
        print("Movement started. Waiting for completion...")
        time.sleep(3)
    
    # ==========================================================================
    # STEP 5: GRIPPER DEMONSTRATIONS
    # ==========================================================================
    print_separator("STEP 5: GRIPPER CONTROL")
    
    wait_for_user("About to demonstrate gripper control. Press Enter to continue...")
    
    # Get current gripper state
    try:
        gripper_state = client.get_gripper_state()
        print(f"Current gripper state: {gripper_state}")
    except Exception as e:
        print(f"⚠️  Could not get gripper state: {e}")
    
    # Open gripper
    print("\n1. Opening gripper...")
    safe_operation("Open Gripper", client.open_gripper)
    time.sleep(2)
    
    # Close gripper
    print("\n2. Closing gripper...")
    safe_operation("Close Gripper", client.close_gripper)
    time.sleep(2)
    
    # Open gripper again
    print("\n3. Opening gripper again...")
    safe_operation("Open Gripper", client.open_gripper)
    time.sleep(2)
    
    # ==========================================================================
    # STEP 6: FINAL STATUS CHECK
    # ==========================================================================
    print_separator("STEP 6: FINAL STATUS CHECK")
    
    try:
        final_state = client.get_state()
        print(f"Final system state: {final_state}")
        
        if client.is_ready():
            print("✅ Recoater is still READY")
        else:
            print("⚠️  Recoater state changed")
            
    except Exception as e:
        print(f"❌ Failed to get final status: {e}")
    
    # ==========================================================================
    # COMPLETION
    # ==========================================================================
    print_separator("DEMONSTRATION COMPLETE")
    print("🎉 Congratulations! You have successfully demonstrated basic recoater control.")
    print()
    print("What you accomplished:")
    print("✅ Connected to the recoater system")
    print("✅ Checked system status")
    print("✅ Controlled X-axis movement and homing")
    print("✅ Controlled Z-axis movement and homing")
    print("✅ Controlled gripper open/close operations")
    print()
    print("Next steps:")
    print("- Study the code to understand how each operation works")
    print("- Try modifying the positions and speeds")
    print("- Explore more advanced features in recoater_client.py")
    print("- Build your own custom control programs")
    print()
    print("Happy recoating! 🚀")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Program interrupted by user (Ctrl+C)")
        print("This is normal - you can stop the program anytime this way.")
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        if DEBUG_MODE:
            import traceback
            traceback.print_exc()
        print("\nPlease check your setup and try again.")
