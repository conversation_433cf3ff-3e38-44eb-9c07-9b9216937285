document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Elements ---
    const connectionStatus = document.getElementById('connection-status');
    const stateValue = document.getElementById('state-value');
    const xPosValue = document.getElementById('x-pos-value');
    const xHomedValue = document.getElementById('x-homed-value');
    const zPosValue = document.getElementById('z-pos-value');
    const zHomedValue = document.getElementById('z-homed-value');
    const logPanel = document.getElementById('log-panel');

    const btnEstop = document.getElementById('btn-estop');
    const btnHomeX = document.getElementById('btn-home-x');
    const btnHomeZ = document.getElementById('btn-home-z');

    const POLLING_INTERVAL = 2000; // 2 seconds

    // --- API Functions ---
    async function pollStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.connection === 'ok') {
                updateStatusUI(data);
            } else {
                handleConnectionError(data);
            }
        } catch (error) {
            handleConnectionError({ message: 'Frontend fetch failed. Is the server running?' });
        }
    }

    async function sendCommand(endpoint) {
        try {
            const response = await fetch(endpoint, { method: 'POST' });
            const result = await response.json();
            log(`Command ${endpoint}: ${result.success ? 'Success' : 'Failed'}`);
            return result;
        } catch (error) {
            log(`Command ${endpoint} failed: ${error}`, 'error');
            return { success: false };
        }
    }

    // --- UI Update Functions ---
    function updateStatusUI(data) {
        connectionStatus.className = 'status-dot-green';
        stateValue.textContent = data.state;
        xPosValue.textContent = `${data.x.position.toFixed(3)} mm`;
        xHomedValue.textContent = data.x.homed;
        zPosValue.textContent = `${data.z.position.toFixed(3)} mm`;
        zHomedValue.textContent = data.z.homed;
        log('Status updated successfully.');
    }
    
    function handleConnectionError(error) {
        connectionStatus.className = 'status-dot-red';
        stateValue.textContent = 'ERROR';
        xPosValue.textContent = 'N/A';
        zPosValue.textContent = 'N/A';
        log(`Connection Error: ${error.message || 'Unknown error'}`, 'error');
    }

    function log(message, type = 'info') {
        logPanel.textContent = message;
        logPanel.className = type === 'error' ? 'log-error' : 'log-info';
    }

    // --- Event Listeners ---
    btnEstop.addEventListener('click', () => {
        if (confirm('Are you sure you want to perform an EMERGENCY STOP?')) {
            sendCommand('/api/emergency_stop');
        }
    });

    btnHomeX.addEventListener('click', () => sendCommand('/api/home_x'));
    btnHomeZ.addEventListener('click', () => sendCommand('/api/home_z'));

    // --- Initialization ---
    setInterval(pollStatus, POLLING_INTERVAL);
    pollStatus(); // Initial call
});
